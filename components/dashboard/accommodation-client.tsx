"use client"

import { useEffect, useState } from "react"
import { useUser } from "@/components/layout/user-context"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { BookingRecord, Hotel, HotelApiResponse, HotelRoomData, RoomType } from "@/types/accommodation"
import { getAuthToken } from "@/utils/auth"
import BookingForm from "./booking-form"
import HotelCard from "./hotel-card"

type ViewMode = "hotels" | "booking" | "bookings"

interface BookingSelection {
  hotel: Hotel
  roomType: RoomType
}

export default function AccommodationClient() {
  const { user } = useUser()
  const { addToast } = useToast()
  const [viewMode, setViewMode] = useState<ViewMode>("hotels")
  const [hotels, setHotels] = useState<Hotel[]>([])
  const [bookings, setBookings] = useState<BookingRecord[]>([])
  const [isLoadingHotels, setIsLoadingHotels] = useState(true)
  const [isLoadingBookings, setIsLoadingBookings] = useState(false)
  const [bookingSelection, setBookingSelection] = useState<BookingSelection | null>(null)

  // Load hotel information from API
  const loadHotels = async () => {
    setIsLoadingHotels(true)
    try {
      const authToken = getAuthToken()
      if (!authToken) {
        addToast({
          type: "error",
          title: "Authentication Error",
          message: "Please login again",
        })
        return
      }

      const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL
      const response = await fetch(`${apiBaseUrl}/api/accommodations/get`, {
        headers: {
          Authorization: `Bearer ${authToken}`,
        },
      })

      const result = (await response.json()) as HotelApiResponse

      if (result.code === 200 && result.data) {
        // Process API data to group by hotel
        const processedHotels = processHotelData(result.data)
        setHotels(processedHotels)
      } else {
        addToast({
          type: "error",
          title: "Load Failed",
          message: result.msg || "Failed to load hotel information",
        })
      }
    } catch (error) {
      console.error("Load hotels error:", error)
      addToast({
        type: "error",
        title: "Load Error",
        message: "An error occurred while loading hotel information. Please try again.",
      })
    } finally {
      setIsLoadingHotels(false)
    }
  }

  // Process API data to group room types by hotel
  const processHotelData = (data: HotelRoomData[]): Hotel[] => {
    const hotelMap = new Map<number, Hotel>()

    data.forEach((item) => {
      const hotelId = item.HotelId

      if (!hotelMap.has(hotelId)) {
        hotelMap.set(hotelId, {
          id: hotelId,
          name: item.name,
          address: item.location,
          tel: item.contact_phone,
          defaultCheckinDate: item.default_checkin_date,
          defaultCheckoutDate: item.default_checkout_date,
          roomTypes: [],
        })
      }

      const hotel = hotelMap.get(hotelId)!
      hotel.roomTypes.push({
        id: `${hotelId}-${item.type}`,
        type: `${item.Name} (${item.total} rooms)`,
        price: item.price,
        total: item.total,
        obligate: item.obligate,
        checkinDate: item.checkin_date,
        checkoutDate: item.checkout_date,
      })
    })

    return Array.from(hotelMap.values())
  }

  // 加载用户预订记录 - 使用本地模拟数据
  const loadBookings = async () => {
    if (!user?.id) return

    setIsLoadingBookings(true)
    try {
      // No bookings data for now
      const mockBookings: BookingRecord[] = []

      // Simulate network delay
      await new Promise((resolve) => setTimeout(resolve, 600))

      setBookings(mockBookings)
    } catch (error) {
      console.error("Load bookings error:", error)
      addToast({
        type: "error",
        title: "Load Error",
        message: "An error occurred while loading your bookings. Please try again.",
      })
    } finally {
      setIsLoadingBookings(false)
    }
  }

  // Handle room booking
  const handleBookRoom = (hotel: Hotel, roomType: RoomType) => {
    setBookingSelection({ hotel, roomType })
    setViewMode("booking")
  }

  // Handle booking success
  const handleBookingSuccess = () => {
    setViewMode("bookings")
    setBookingSelection(null)
    loadBookings() // 重新加载预订记录
  }

  // Handle cancel booking
  const handleCancelBooking = () => {
    setViewMode("hotels")
    setBookingSelection(null)
  }

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  // Get status badge styles
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { label: "Pending", className: "bg-yellow-100 text-yellow-800", icon: "fas fa-clock" },
      confirmed: { label: "Confirmed", className: "bg-green-100 text-green-800", icon: "fas fa-check-circle" },
      cancelled: { label: "Cancelled", className: "bg-red-100 text-red-800", icon: "fas fa-times-circle" },
      completed: { label: "Completed", className: "bg-blue-100 text-blue-800", icon: "fas fa-flag-checkered" },
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending
    return (
      <Badge className={config.className}>
        <i className={`${config.icon} mr-1`}></i>
        {config.label}
      </Badge>
    )
  }

  // Initialize loading
  useEffect(() => {
    loadHotels()
  }, [])

  // Load data when switching to bookings view
  useEffect(() => {
    if (viewMode === "bookings") {
      loadBookings()
    }
  }, [viewMode, user?.id])

  if (!user) {
    return (
      <div className="py-12 text-center">
        <p className="text-gray-500">Please login to access accommodation booking.</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Navigation tabs */}
      <div className="flex space-x-1 rounded-lg bg-gray-100 p-1">
        <button
          onClick={() => setViewMode("hotels")}
          className={`flex-1 rounded-md px-3 py-2 text-sm font-medium transition-colors ${
            viewMode === "hotels" ? "bg-white text-gray-900 shadow-sm" : "text-gray-600 hover:text-gray-900"
          }`}
        >
          <i className="fas fa-hotel mr-2"></i>
          Browse Hotels
        </button>
        <button
          onClick={() => setViewMode("bookings")}
          className={`flex-1 rounded-md px-3 py-2 text-sm font-medium transition-colors ${
            viewMode === "bookings" ? "bg-white text-gray-900 shadow-sm" : "text-gray-600 hover:text-gray-900"
          }`}
        >
          <i className="fas fa-bed mr-2"></i>
          My Bookings
        </button>
      </div>

      {/* Hotels browsing view */}
      {viewMode === "hotels" && (
        <div>
          <div className="mb-6">
            <h2 className="text-xl font-bold text-gray-900">Conference Hotels</h2>
            <p className="text-gray-600">Special rates for IFMB 2025 attendees</p>
          </div>

          {isLoadingHotels ? (
            <div className="py-12 text-center">
              <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-r-transparent"></div>
              <p className="mt-2 text-gray-500">Loading hotels...</p>
            </div>
          ) : hotels.length > 0 ? (
            <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
              {hotels.map((hotel) => (
                <HotelCard key={hotel.id} hotel={hotel} onBookRoom={handleBookRoom} />
              ))}
            </div>
          ) : (
            <div className="py-12 text-center">
              <i className="fas fa-hotel mb-4 text-5xl text-gray-400"></i>
              <p className="text-gray-500">No hotels available at the moment.</p>
            </div>
          )}
        </div>
      )}

      {/* Booking form view */}
      {viewMode === "booking" && bookingSelection && (
        <div>
          <div className="mb-6">
            <Button variant="outline" onClick={() => setViewMode("hotels")} className="mb-4">
              <i className="fas fa-arrow-left mr-2"></i>
              Back to Hotels
            </Button>
          </div>

          <BookingForm
            hotel={bookingSelection.hotel}
            roomType={bookingSelection.roomType}
            onBookingSuccess={handleBookingSuccess}
            onCancel={handleCancelBooking}
          />
        </div>
      )}

      {/* My bookings view */}
      {viewMode === "bookings" && (
        <div>
          <div className="mb-6">
            <h2 className="text-xl font-bold text-gray-900">My Bookings</h2>
            <p className="text-gray-600">Manage your hotel reservations</p>
          </div>

          {isLoadingBookings ? (
            <div className="py-12 text-center">
              <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-r-transparent"></div>
              <p className="mt-2 text-gray-500">Loading bookings...</p>
            </div>
          ) : bookings.length > 0 ? (
            <div className="space-y-4">
              {bookings.map((booking) => (
                <Card key={booking.booking_id}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">{booking.hotel_name}</CardTitle>
                      {getStatusBadge(booking.status)}
                    </div>
                    <CardDescription>Booking #{booking.confirmation_number || booking.booking_id}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    {/* Booking basic information */}
                    <div className="mb-4 rounded-lg bg-gray-50 p-4">
                      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <div>
                          <p className="text-sm text-gray-600">Room Type</p>
                          <p className="font-medium">{booking.room_type}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Check-in Dates</p>
                          <p className="font-medium">
                            {formatDate(booking.check_in_date)} - {formatDate(booking.check_out_date)}
                          </p>
                          <p className="text-xs text-gray-500">
                            {Math.ceil(
                              (new Date(booking.check_out_date).getTime() - new Date(booking.check_in_date).getTime()) /
                                (1000 * 60 * 60 * 24)
                            )}{" "}
                            nights
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Number of Guests</p>
                          <p className="font-medium">{booking.guests_count} guest(s)</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Total Price</p>
                          <p className="text-lg font-bold text-green-600">¥{booking.total_price}</p>
                          <p className="text-xs text-gray-500">Conference rate</p>
                        </div>
                      </div>
                    </div>

                    {/* Companions information */}
                    {booking.companions.length > 0 && (
                      <div className="mb-4">
                        <p className="mb-2 text-sm text-gray-600">
                          <i className="fas fa-users mr-1"></i>
                          Companions ({booking.companions.length})
                        </p>
                        <div className="flex flex-wrap gap-2">
                          {booking.companions.map((companion, index) => (
                            <Badge key={index} variant="outline" className="bg-blue-50">
                              <i className="fas fa-user mr-1"></i>
                              {companion}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Special requests */}
                    {booking.special_requests && (
                      <div className="mb-4">
                        <p className="mb-1 text-sm text-gray-600">
                          <i className="fas fa-comment mr-1"></i>
                          Special Requests
                        </p>
                        <p className="rounded bg-gray-50 p-2 text-sm">{booking.special_requests}</p>
                      </div>
                    )}

                    {/* Booking information and actions */}
                    <div className="flex items-center justify-between border-t pt-4">
                      <div className="text-xs text-gray-500">
                        <p>
                          <i className="fas fa-calendar mr-1"></i>
                          Booked on: {formatDate(booking.booking_time)}
                        </p>
                        {booking.confirmation_number && (
                          <p className="mt-1">
                            <i className="fas fa-receipt mr-1"></i>
                            Confirmation: {booking.confirmation_number}
                          </p>
                        )}
                      </div>

                      {/* Action buttons */}
                      <div className="flex gap-2">
                        {booking.status === "confirmed" && (
                          <Button variant="outline" size="sm">
                            <i className="fas fa-edit mr-1"></i>
                            Modify
                          </Button>
                        )}
                        {booking.status === "pending" && (
                          <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                            <i className="fas fa-times mr-1"></i>
                            Cancel
                          </Button>
                        )}
                        <Button variant="outline" size="sm">
                          <i className="fas fa-download mr-1"></i>
                          Receipt
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="py-12 text-center">
                <i className="fas fa-bed mb-4 text-5xl text-gray-400"></i>
                <p className="mb-2 text-lg font-medium text-gray-500">No bookings yet</p>
                <p className="mb-6 text-gray-500">You haven't made any hotel reservations</p>
                <Button onClick={() => setViewMode("hotels")}>
                  <i className="fas fa-search mr-2"></i>
                  Browse Hotels
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  )
}
