"use client"

import { useState } from "react"
import { useUser } from "@/components/layout/user-context"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useToast } from "@/hooks/use-toast"
import { BookingFormData, BookingRequest, Hotel, RoomType } from "@/types/accommodation"
import { getAuthToken } from "@/utils/auth"

interface BookingFormProps {
  hotel: Hotel
  roomType: RoomType
  onBookingSuccess: () => void
  onCancel: () => void
}

export default function BookingForm({ hotel, roomType, onBookingSuccess, onCancel }: BookingFormProps) {
  const { user } = useUser()
  const { addToast } = useToast()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isValidatingUsername, setIsValidatingUsername] = useState(false)

  // Form data state
  const [formData, setFormData] = useState<BookingFormData>({
    checkinDate: hotel.defaultCheckinDate ? hotel.defaultCheckinDate.split("T")[0] || "2025-10-16" : "2025-10-16",
    checkoutDate: hotel.defaultCheckoutDate ? hotel.defaultCheckoutDate.split("T")[0] || "2025-10-19" : "2025-10-19",
    sharedOptions: "none",
    partnerUsername: "",
  })

  // Calculate total price
  const calculateTotalPrice = () => {
    if (!formData.checkinDate || !formData.checkoutDate) return roomType.price
    const checkIn = new Date(formData.checkinDate)
    const checkOut = new Date(formData.checkoutDate)
    const nights = Math.ceil((checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24))
    return roomType.price * Math.max(1, nights)
  }

  // Calculate number of nights
  const calculateNights = () => {
    if (!formData.checkinDate || !formData.checkoutDate) return 1
    const checkIn = new Date(formData.checkinDate)
    const checkOut = new Date(formData.checkoutDate)
    return Math.ceil((checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24))
  }

  // Validate partner username and get user ID
  const validatePartnerUsername = async (username: string): Promise<{ isValid: boolean; userId?: number }> => {
    if (!username.trim()) return { isValid: false }

    setIsValidatingUsername(true)
    try {
      const authToken = getAuthToken()
      if (!authToken) {
        return { isValid: false }
      }

      const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL
      const response = await fetch(
        `${apiBaseUrl}/api/accommodations/validate-username?username=${encodeURIComponent(username.trim())}`,
        {
          headers: {
            Authorization: `Bearer ${authToken}`,
          },
        }
      )

      const result = (await response.json()) as {
        code: number
        data?: { exists: boolean; user_id?: number }
        msg?: string
      }

      if (result.code === 200 && result.data) {
        return {
          isValid: result.data.exists,
          userId: result.data.user_id,
        }
      }

      return { isValid: false }
    } catch (error) {
      console.error("Username validation error:", error)
      return { isValid: false }
    } finally {
      setIsValidatingUsername(false)
    }
  }

  // Validate partner username when shared option is "with_partner"
  const validatePartner = async (): Promise<{ isValid: boolean; partnerUserId?: number }> => {
    if (formData.sharedOptions === "with_partner") {
      if (!formData.partnerUsername.trim()) {
        addToast({
          type: "error",
          title: "Partner Required",
          message: "Please enter your partner's username",
        })
        return { isValid: false }
      }

      const validation = await validatePartnerUsername(formData.partnerUsername.trim())
      if (!validation.isValid) {
        addToast({
          type: "error",
          title: "Partner Not Found",
          message: "The partner username does not exist in our system",
        })
        return { isValid: false }
      }

      return { isValid: true, partnerUserId: validation.userId }
    }
    return { isValid: true }
  }

  // Submit booking
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validate form
    if (!formData.checkinDate || !formData.checkoutDate) {
      addToast({
        type: "error",
        title: "Invalid Dates",
        message: "Please select both check-in and check-out dates",
      })
      return
    }

    if (new Date(formData.checkinDate) >= new Date(formData.checkoutDate)) {
      addToast({
        type: "error",
        title: "Invalid Dates",
        message: "Check-out date must be after check-in date",
      })
      return
    }

    // Validate partner if needed
    const partnerValidation = await validatePartner()
    if (!partnerValidation.isValid) {
      return
    }

    if (!user?.id) {
      addToast({
        type: "error",
        title: "Authentication Error",
        message: "Please login again",
      })
      return
    }

    setIsSubmitting(true)

    try {
      const authToken = getAuthToken()
      if (!authToken) {
        addToast({
          type: "error",
          title: "Authentication Error",
          message: "Please login again",
        })
        return
      }

      // Convert UI form data to API format
      const sharedOptionMap = {
        none: 1,
        with_partner: 2,
        system_assigned: 3,
      } as const

      const bookingData: BookingRequest = {
        room_id: parseInt(roomType.id.split("-")[1] || "1") || 1, // Extract room type number from ID
        shared_option: sharedOptionMap[formData.sharedOptions],
        occupant: parseInt(user.id),
        checkin_date: formData.checkinDate,
        checkout_date: formData.checkoutDate,
        is_assigned: formData.sharedOptions === "system_assigned",
        partner_user_id: partnerValidation.partnerUserId,
      }

      const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL
      const response = await fetch(`${apiBaseUrl}/api/accommodations/submit`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${authToken}`,
        },
        body: JSON.stringify(bookingData),
      })

      const result = (await response.json()) as { code: number; msg?: string; data?: unknown }

      if (result.code === 200) {
        addToast({
          type: "success",
          title: "Booking Successful",
          message: result.msg || "Your accommodation booking has been submitted successfully.",
        })
        onBookingSuccess()
      } else {
        addToast({
          type: "error",
          title: "Booking Failed",
          message: result.msg || "Failed to submit your booking. Please try again.",
        })
      }
    } catch (error) {
      console.error("Booking submission error:", error)
      addToast({
        type: "error",
        title: "Booking Failed",
        message: "An error occurred while submitting your booking. Please try again.",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Book Your Stay</CardTitle>
        <CardDescription>
          Complete your booking for {hotel.name} - {roomType.type}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Hotel and room type information */}
          <div className="rounded-lg bg-gray-50 p-4">
            <h4 className="font-medium text-gray-900">{hotel.name}</h4>
            <p className="text-sm text-gray-600">{roomType.type}</p>
            <p className="text-lg font-bold text-gray-900">¥{roomType.price}/night</p>
          </div>

          {/* Check-in and check-out dates */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="checkinDate">Check-in Date *</Label>
              <Input
                id="checkinDate"
                type="date"
                value={formData.checkinDate}
                onChange={(e) => setFormData({ ...formData, checkinDate: e.target.value })}
                min="2025-10-15"
                max="2025-10-20"
                required
              />
            </div>
            <div>
              <Label htmlFor="checkoutDate">Check-out Date *</Label>
              <Input
                id="checkoutDate"
                type="date"
                value={formData.checkoutDate}
                onChange={(e) => setFormData({ ...formData, checkoutDate: e.target.value })}
                min="2025-10-16"
                max="2025-10-21"
                required
              />
            </div>
          </div>

          {/* Shared room options */}
          <div>
            <Label>Room Sharing Options *</Label>
            <div className="mt-2 space-y-3">
              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  id="none"
                  name="sharedOptions"
                  value="none"
                  checked={formData.sharedOptions === "none"}
                  onChange={(e) =>
                    setFormData({ ...formData, sharedOptions: e.target.value as BookingFormData["sharedOptions"] })
                  }
                />
                <Label htmlFor="none" className="cursor-pointer">
                  No sharing (single occupancy)
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  id="with_partner"
                  name="sharedOptions"
                  value="with_partner"
                  checked={formData.sharedOptions === "with_partner"}
                  onChange={(e) =>
                    setFormData({ ...formData, sharedOptions: e.target.value as BookingFormData["sharedOptions"] })
                  }
                />
                <Label htmlFor="with_partner" className="cursor-pointer">
                  Share with partner
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  id="system_assigned"
                  name="sharedOptions"
                  value="system_assigned"
                  checked={formData.sharedOptions === "system_assigned"}
                  onChange={(e) =>
                    setFormData({ ...formData, sharedOptions: e.target.value as BookingFormData["sharedOptions"] })
                  }
                />
                <Label htmlFor="system_assigned" className="cursor-pointer">
                  System assigned roommate
                </Label>
              </div>
            </div>

            {/* Partner username input */}
            {formData.sharedOptions === "with_partner" && (
              <div className="mt-4">
                <Label htmlFor="partnerUsername">Partner Username *</Label>
                <Input
                  id="partnerUsername"
                  placeholder="Enter your partner's username"
                  value={formData.partnerUsername}
                  onChange={(e) => setFormData({ ...formData, partnerUsername: e.target.value })}
                  required
                />
                {isValidatingUsername && <p className="mt-1 text-sm text-gray-500">Validating username...</p>}
              </div>
            )}
          </div>

          {/* Price summary */}
          <div className="rounded-lg border bg-gray-50 p-4">
            <div className="flex items-center justify-between">
              <span className="font-medium">Total ({calculateNights()} nights):</span>
              <span className="text-xl font-bold">¥{calculateTotalPrice()}</span>
            </div>
          </div>

          {/* Buttons */}
          <div className="flex gap-3">
            <Button type="button" variant="outline" onClick={onCancel} className="flex-1">
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting} className="flex-1">
              {isSubmitting ? "Booking..." : "Confirm Booking"}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
