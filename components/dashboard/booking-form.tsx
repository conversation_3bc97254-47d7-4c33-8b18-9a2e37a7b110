"use client"

import { useState } from "react"
import { useUser } from "@/components/layout/user-context"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useToast } from "@/hooks/use-toast"
import { BookingRequest, Hotel, RoomType, SharedOption } from "@/types/accommodation"

interface BookingFormProps {
  hotel: Hotel
  roomType: RoomType
  onBookingSuccess: () => void
  onCancel: () => void
}

export default function BookingForm({ hotel, roomType, onBookingSuccess, onCancel }: BookingFormProps) {
  const { user } = useUser()
  const { addToast } = useToast()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isValidatingUsername, setIsValidatingUsername] = useState(false)

  // Form data state
  const [formData, setFormData] = useState({
    checkinDate: "2025-10-16", // Default conference start date
    checkoutDate: "2025-10-19", // Default conference end date
    sharedOptions: "none" as SharedOption,
    partnerUsername: "",
  })

  // Calculate total price
  const calculateTotalPrice = () => {
    const checkIn = new Date(formData.checkinDate)
    const checkOut = new Date(formData.checkoutDate)
    const nights = Math.ceil((checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24))
    return roomType.price * Math.max(1, nights)
  }

  // Calculate number of nights
  const calculateNights = () => {
    const checkIn = new Date(formData.checkinDate)
    const checkOut = new Date(formData.checkoutDate)
    return Math.ceil((checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24))
  }

  // Validate partner username - use local mock data
  const validatePartnerUsername = async (username: string): Promise<boolean> => {
    if (!username.trim()) return false

    setIsValidatingUsername(true)
    try {
      // Mock username validation
      const mockValidUsernames = [
        "john_doe",
        "jane_smith",
        "test_user",
        "demo_user",
        "alice_wang",
        "bob_chen",
        "charlie_li",
        "diana_zhang",
      ]

      // Simulate network delay
      await new Promise((resolve) => setTimeout(resolve, 500))

      return mockValidUsernames.includes(username.trim().toLowerCase())
    } catch (error) {
      console.error("Username validation error:", error)
      return false
    } finally {
      setIsValidatingUsername(false)
    }
  }

  // Validate partner username when shared option is "with_partner"
  const validatePartner = async (): Promise<boolean> => {
    if (formData.sharedOptions === "with_partner") {
      if (!formData.partnerUsername.trim()) {
        addToast({
          type: "error",
          title: "Partner Required",
          message: "Please enter your partner's username",
        })
        return false
      }

      const isValid = await validatePartnerUsername(formData.partnerUsername.trim())
      if (!isValid) {
        addToast({
          type: "error",
          title: "Partner Not Found",
          message: "The partner username does not exist in our system",
        })
        return false
      }
    }
    return true
  }

  // Submit booking
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validate form
    if (new Date(formData.checkinDate) >= new Date(formData.checkoutDate)) {
      addToast({
        type: "error",
        title: "Invalid Dates",
        message: "Check-out date must be after check-in date",
      })
      return
    }

    // Validate partner if needed
    const isPartnerValid = await validatePartner()
    if (!isPartnerValid) {
      return
    }

    if (!user?.id) {
      addToast({
        type: "error",
        title: "Authentication Error",
        message: "Please login again",
      })
      return
    }

    setIsSubmitting(true)

    try {
      // Simulate booking submission
      const bookingData: BookingRequest = {
        room_id: roomType.id,
        user_id: user.id,
        shared_options: formData.sharedOptions,
        partner_username: formData.sharedOptions === "with_partner" ? formData.partnerUsername.trim() : undefined,
        checkin_date: formData.checkinDate,
        checkout_date: formData.checkoutDate,
      }

      // Simulate network delay
      await new Promise((resolve) => setTimeout(resolve, 1500))

      // Simulate successful booking
      console.log("Mock booking data:", bookingData)

      addToast({
        type: "success",
        title: "Booking Successful",
        message: "Your accommodation booking has been submitted successfully. We will confirm it shortly.",
      })
      onBookingSuccess()
    } catch (error) {
      console.error("Booking submission error:", error)
      addToast({
        type: "error",
        title: "Booking Failed",
        message: "An error occurred while submitting your booking. Please try again.",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Book Your Stay</CardTitle>
        <CardDescription>
          Complete your booking for {hotel.name} - {roomType.type}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Hotel and room type information */}
          <div className="rounded-lg bg-gray-50 p-4">
            <h4 className="font-medium text-gray-900">{hotel.name}</h4>
            <p className="text-sm text-gray-600">{roomType.type}</p>
            <p className="text-lg font-bold text-gray-900">¥{roomType.price}/night</p>
          </div>

          {/* Check-in and check-out dates */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="checkinDate">Check-in Date *</Label>
              <Input
                id="checkinDate"
                type="date"
                value={formData.checkinDate}
                onChange={(e) => setFormData({ ...formData, checkinDate: e.target.value })}
                min="2025-10-15"
                max="2025-10-20"
                required
              />
            </div>
            <div>
              <Label htmlFor="checkoutDate">Check-out Date *</Label>
              <Input
                id="checkoutDate"
                type="date"
                value={formData.checkoutDate}
                onChange={(e) => setFormData({ ...formData, checkoutDate: e.target.value })}
                min="2025-10-16"
                max="2025-10-21"
                required
              />
            </div>
          </div>

          {/* Shared room options */}
          <div>
            <Label>Room Sharing Options *</Label>
            <div className="mt-2 space-y-3">
              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  id="none"
                  name="sharedOptions"
                  value="none"
                  checked={formData.sharedOptions === "none"}
                  onChange={(e) => setFormData({ ...formData, sharedOptions: e.target.value as SharedOption })}
                />
                <Label htmlFor="none" className="cursor-pointer">
                  No sharing (single occupancy)
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  id="with_partner"
                  name="sharedOptions"
                  value="with_partner"
                  checked={formData.sharedOptions === "with_partner"}
                  onChange={(e) => setFormData({ ...formData, sharedOptions: e.target.value as SharedOption })}
                />
                <Label htmlFor="with_partner" className="cursor-pointer">
                  Share with partner
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  id="system_assigned"
                  name="sharedOptions"
                  value="system_assigned"
                  checked={formData.sharedOptions === "system_assigned"}
                  onChange={(e) => setFormData({ ...formData, sharedOptions: e.target.value as SharedOption })}
                />
                <Label htmlFor="system_assigned" className="cursor-pointer">
                  System assigned roommate
                </Label>
              </div>
            </div>

            {/* Partner username input */}
            {formData.sharedOptions === "with_partner" && (
              <div className="mt-4">
                <Label htmlFor="partnerUsername">Partner Username *</Label>
                <Input
                  id="partnerUsername"
                  placeholder="Enter your partner's username"
                  value={formData.partnerUsername}
                  onChange={(e) => setFormData({ ...formData, partnerUsername: e.target.value })}
                  required
                />
                {isValidatingUsername && <p className="mt-1 text-sm text-gray-500">Validating username...</p>}
              </div>
            )}
          </div>

          {/* Price summary */}
          <div className="rounded-lg border bg-gray-50 p-4">
            <div className="flex items-center justify-between">
              <span className="font-medium">Total ({calculateNights()} nights):</span>
              <span className="text-xl font-bold">¥{calculateTotalPrice()}</span>
            </div>
          </div>

          {/* Buttons */}
          <div className="flex gap-3">
            <Button type="button" variant="outline" onClick={onCancel} className="flex-1">
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting} className="flex-1">
              {isSubmitting ? "Booking..." : "Confirm Booking"}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
