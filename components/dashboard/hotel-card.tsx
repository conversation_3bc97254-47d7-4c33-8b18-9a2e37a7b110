"use client"

import { useState } from "react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Hotel, RoomType } from "@/types/accommodation"

interface HotelCardProps {
  hotel: Hotel
  onBookRoom: (hotel: Hotel, roomType: RoomType) => void
}

export default function HotelCard({ hotel, onBookRoom }: HotelCardProps) {
  const [selectedRoomType, setSelectedRoomType] = useState<RoomType | null>(null)

  const handleBookNow = () => {
    if (selectedRoomType) {
      onBookRoom(hotel, selectedRoomType)
    }
  }

  return (
    <Card className="overflow-hidden">
      {/* Hotel image */}
      <div className="relative h-48 bg-gray-200">
        {hotel.id === 1 && (
          <div className="absolute top-3 left-3 z-10">
            <Badge className="bg-green-600">Recommended</Badge>
          </div>
        )}
        <div
          className="h-full w-full bg-cover bg-center"
          style={{
            backgroundImage: `url('https://minioapi.bugmaker.me/ifmb-2025-public/hotels/hotel${hotel.id}.jpg')`,
          }}
        />
      </div>

      <CardHeader>
        <CardTitle className="text-lg">{hotel.name}</CardTitle>
        <CardDescription className="text-sm">
          <div className="space-y-1">
            <div className="flex items-center">
              <i className="fas fa-map-marker-alt mr-2 text-gray-400"></i>
              <span>{hotel.distance}</span>
            </div>
            <div className="flex items-center">
              <i className="fas fa-phone mr-2 text-gray-400"></i>
              <span>{hotel.tel}</span>
            </div>
          </div>
        </CardDescription>
      </CardHeader>

      <CardContent>
        {/* Room type selection */}
        <div className="space-y-3">
          <h4 className="font-medium text-gray-900">Available Room Types</h4>
          {hotel.roomTypes.map((roomType, index) => (
            <div
              key={index}
              className={`cursor-pointer rounded-lg border p-3 transition-colors ${
                selectedRoomType?.id === roomType.id
                  ? "border-blue-500 bg-blue-50"
                  : "border-gray-200 hover:border-gray-300"
              }`}
              onClick={() => setSelectedRoomType(roomType)}
            >
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center">
                    <input
                      type="radio"
                      name={`room-${hotel.id}`}
                      checked={selectedRoomType?.id === roomType.id}
                      onChange={() => setSelectedRoomType(roomType)}
                      className="mr-3"
                    />
                    <div>
                      <p className="font-medium text-gray-900">{roomType.type}</p>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-lg font-bold text-gray-900">
                    ¥{roomType.price}
                    <span className="text-sm font-normal text-gray-500">/night</span>
                  </p>
                  <p className="text-xs text-green-600">Conference rate</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Booking button */}
        <div className="mt-4">
          <Button className="w-full" onClick={handleBookNow} disabled={!selectedRoomType}>
            <i className="fas fa-hotel mr-2"></i>
            {selectedRoomType ? "Book Selected Room" : "Select Room Type"}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
