/**
 * 住宿预订相关的类型定义
 */

// API response from backend
export interface HotelApiResponse {
  code: number
  msg: string
  data: HotelRoomData[]
}

// Hotel room data from API
export interface HotelRoomData {
  name: string
  location: string
  contact_name: string
  contact_phone: string
  default_checkin_date: string
  default_checkout_date: string
  HotelId: number
  type: string
  price: number
  total: number
  obligate: number
  checkin_date: string
  checkout_date: string
  Name: string // Room type name
}

// Processed hotel information for UI
export interface Hotel {
  id: number
  name: string
  address: string
  tel: string
  defaultCheckinDate?: string
  defaultCheckoutDate?: string
  roomTypes: RoomType[]
}

// Room type information
export interface RoomType {
  id: string // Room type ID
  type: string // Room type name
  price: number
  total: number
  obligate: number
  checkinDate: string
  checkoutDate: string
}

// Shared room options
export type SharedOption = "none" | "with_partner" | "system_assigned"

// Booking request type
export interface BookingRequest {
  room_id: string // Room ID (not displayed)
  user_id: string // User ID (not displayed)
  shared_options: SharedOption
  partner_username?: string // Only when shared_options is "with_partner"
  checkin_date: string
  checkout_date: string
}

// 预订记录类型
export interface BookingRecord {
  booking_id: string
  user_id: number
  hotel_id: number
  hotel_name: string
  room_type: string
  check_in_date: string
  check_out_date: string
  guests_count: number
  companions: string[]
  total_price: number
  currency: string
  status: "pending" | "confirmed" | "cancelled" | "completed"
  special_requests?: string
  booking_time: string
  confirmation_number?: string
}

// API 响应类型
export interface ApiResponse<T> {
  code: number
  data: T
  msg: string
  message?: string
}

// 用户名验证响应类型
export interface UsernameValidationResponse {
  username: string
  exists: boolean
  name?: string
}
