/**
 * 住宿预订相关的类型定义
 */

// Hotel information type - consistent with transportation page
export interface Hotel {
  id: number
  name: string
  address: string
  viewOnMapName: string
  tel: string
  distance: string
  coordinates: {
    lat: number
    lng: number
  }
  roomTypes: RoomType[]
}

// Room type information - consistent with transportation page
export interface RoomType {
  id: string // Room ID (not displayed to user)
  type: string
  price: number
}

// Shared room options
export type SharedOption = "none" | "with_partner" | "system_assigned"

// Booking request type
export interface BookingRequest {
  room_id: string // Room ID (not displayed)
  user_id: string // User ID (not displayed)
  shared_options: SharedOption
  partner_username?: string // Only when shared_options is "with_partner"
  checkin_date: string
  checkout_date: string
}

// 预订记录类型
export interface BookingRecord {
  booking_id: string
  user_id: number
  hotel_id: number
  hotel_name: string
  room_type: string
  check_in_date: string
  check_out_date: string
  guests_count: number
  companions: string[]
  total_price: number
  currency: string
  status: "pending" | "confirmed" | "cancelled" | "completed"
  special_requests?: string
  booking_time: string
  confirmation_number?: string
}

// API 响应类型
export interface ApiResponse<T> {
  code: number
  data: T
  msg: string
  message?: string
}

// 用户名验证响应类型
export interface UsernameValidationResponse {
  username: string
  exists: boolean
  name?: string
}
